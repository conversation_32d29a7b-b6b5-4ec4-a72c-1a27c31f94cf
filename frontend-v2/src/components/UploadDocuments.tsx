import React, { useState } from 'react';
import { Upload, File, X, CheckCircle, FileDown } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

const API_BASE = import.meta.env.VITE_API_BASE_URL;

const defaultColumns = [
  "Test Case ID",
  "Type",
  "Case frequency",
  "Test Case Description",
  "Preconditions",
  "Test Steps",
  "Test Data",
  "Expected Result",
  "Actual Result",
  "Status (Pass/Fail)",
  "Comments"
];

const UploadDocuments = () => {
  const [dragActive, setDragActive] = useState(false);
  const [files, setFiles] = useState<File[]>([]);
  const [selectedTestTypes, setSelectedTestTypes] = useState<string[]>(['Functional', 'Integration']);
  const [detailLevel, setDetailLevel] = useState('Detailed');
  const [selectedCaseFrequencies, setSelectedCaseFrequencies] = useState<string[]>(['Smoke']);
  const [loading, setLoading] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [requirementsText, setRequirementsText] = useState<string | null>(null);
  const [testCases, setTestCases] = useState<any[] | null>(null);
  const [pdfLoading, setPdfLoading] = useState(false);
  const [baseFileName, setBaseFileName] = useState<string>('Testify');
  const [columns, setColumns] = useState<string[]>([...defaultColumns]);
  const [columnInput, setColumnInput] = useState<string>(defaultColumns.join(", "));

  const testTypeOptions = [
    'Functional',
    'Performance', 
    'Security',
    'Usability',
    'Integration',
    'API'
  ];

  // Upload the first file as soon as files are selected
  const handleFilesUpload = async (newFiles: File[]) => {
    setFiles(newFiles);
    setRequirementsText(null);
    setTestCases(null);
    setError(null);
    // Set base file name from first file (without extension)
    if (newFiles.length > 0) {
      const name = newFiles[0].name;
      const dotIdx = name.lastIndexOf(".");
      setBaseFileName(dotIdx > 0 ? name.substring(0, dotIdx) : name);
    }
    if (newFiles.length === 0) return;
    setUploading(true);
    try {
      const formData = new FormData();
      formData.append("file", newFiles[0]);
      const uploadRes = await fetch(`${API_BASE}/upload`, {
        method: "POST",
        body: formData,
      });
      const uploadData = await uploadRes.json();
      if (!uploadData.success) {
        throw new Error(uploadData.error || "Failed to process document.");
      }
      setRequirementsText(uploadData.requirements_text);
    } catch (err: any) {
      setError(err.message || "An error occurred during upload.");
    } finally {
      setUploading(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      const newFiles = Array.from(e.dataTransfer.files);
      handleFilesUpload(newFiles);
    }
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const newFiles = Array.from(e.target.files);
      handleFilesUpload(newFiles);
    }
  };

  const removeFile = (index: number) => {
    const newFiles = files.filter((_, i) => i !== index);
    setFiles(newFiles);
    setRequirementsText(null);
    setTestCases(null);
    setError(null);
    if (newFiles.length > 0) {
      handleFilesUpload(newFiles);
    }
  };

  const toggleTestType = (testType: string) => {
    setSelectedTestTypes(prev => 
      prev.includes(testType) 
        ? prev.filter(type => type !== testType)
        : [...prev, testType]
    );
  };

  const generateTestCases = async () => {
    setError(null);
    setTestCases(null);

    if (!requirementsText) {
      setError("Please upload and process a document first.");
      return;
    }
    setLoading(true);

    try {
      const genForm = new FormData();
      genForm.append("requirements_text", requirementsText);
      selectedTestTypes.forEach(type => genForm.append("test_types", type));
      genForm.append("detail_level", detailLevel);
      selectedCaseFrequencies.forEach(freq => genForm.append("case_frequency", freq));
      columns.forEach(col => genForm.append("columns", col));

      const genRes = await fetch(`${API_BASE}/generate`, {
        method: "POST",
        body: genForm,
      });
      const genData = await genRes.json();
      if (!genData.success) {
        throw new Error(genData.error || "Failed to generate test cases.");
      }
      setTestCases(genData.test_cases);
    } catch (err: any) {
      setError(err.message || "An error occurred.");
    } finally {
      setLoading(false);
    }
  };

  const downloadPdf = async () => {
    if (!testCases || testCases.length === 0) return;
    setPdfLoading(true);
    setError(null);
    try {
      const res = await fetch(`${API_BASE}/export/pdf`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify(testCases)
      });
      if (!res.ok) {
        throw new Error("Failed to generate PDF.");
      }
      const blob = await res.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `Test cases_${baseFileName}.pdf`;
      document.body.appendChild(a);
      a.click();
      a.remove();
      window.URL.revokeObjectURL(url);
    } catch (err: any) {
      setError(err.message || "Failed to download PDF.");
    } finally {
      setPdfLoading(false);
    }
  };

  const downloadExcel = async () => {
    if (!testCases || testCases.length === 0) return;
    setPdfLoading(true);
    setError(null);
    try {
      const res = await fetch(`${API_BASE}/export/excel`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({ test_cases: testCases })
      });
      if (!res.ok) {
        throw new Error("Failed to generate Excel file.");
      }
      const blob = await res.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `Test cases_${baseFileName}.xlsx`;
      document.body.appendChild(a);
      a.click();
      a.remove();
      window.URL.revokeObjectURL(url);
    } catch (err: any) {
      setError(err.message || "Failed to download Excel file.");
    } finally {
      setPdfLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div
        className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
          dragActive
            ? 'border-blue-500 bg-blue-50'
            : 'border-gray-300 hover:border-gray-400'
        }`}
        onDragEnter={e => {
          e.preventDefault();
          e.stopPropagation();
          setDragActive(true);
        }}
        onDragLeave={e => {
          e.preventDefault();
          e.stopPropagation();
          setDragActive(false);
        }}
        onDragOver={e => {
          e.preventDefault();
          e.stopPropagation();
        }}
        onDrop={handleDrop}
      >
        <Upload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          Upload PRD & BRD Documents
        </h3>
        <p className="text-gray-600 mb-4">
          Drag and drop your documents here, or click to browse
        </p>
        <input
          type="file"
          multiple
          accept=".pdf,.doc,.docx,.txt"
          onChange={handleFileSelect}
          className="hidden"
          id="file-upload"
        />
        <label
          htmlFor="file-upload"
          className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 cursor-pointer"
        >
          Choose Files
        </label>
        <p className="text-xs text-gray-500 mt-2">
          Supported formats: PDF, DOC, DOCX, TXT
        </p>
      </div>

      {files.length > 0 && (
        <div className="space-y-4">
          <div className="space-y-3">
            <h4 className="font-medium text-gray-900">Uploaded Files</h4>
            {files.map((file, index) => (
              <div
                key={index}
                className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
              >
                <div className="flex items-center space-x-3">
                  <File className="h-5 w-5 text-blue-600" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">{file.name}</p>
                    <p className="text-xs text-gray-500">
                      {(file.size / 1024 / 1024).toFixed(2)} MB
                    </p>
                  </div>
                </div>
                <button
                  onClick={() => removeFile(index)}
                  className="text-gray-400 hover:text-red-500 transition-colors"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>
            ))}
          </div>

          {/* Column Selection */}
          <div className="p-4 bg-gray-50 rounded-lg mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Columns to include in the Excel:
            </label>
            <input
              type="text"
              value={columnInput}
              onChange={e => {
                setColumnInput(e.target.value);
                setColumns(
                  e.target.value
                    .split(",")
                    .map(s => s.trim())
                    .filter(s => s.length > 0)
                );
              }}
              className="w-full border border-gray-300 rounded px-3 py-2 text-sm"
              placeholder="Enter column names separated by commas"
            />
            <div className="text-xs text-gray-500 mt-1">
              Default: {defaultColumns.join(", ")}
            </div>
          </div>
          {/* Test Type and Detail Level Selection */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 p-4 bg-gray-50 rounded-lg">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Select test types to generate:
              </label>
              <div className="space-y-2">
                {testTypeOptions.map((testType) => (
                  <label key={testType} className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={selectedTestTypes.includes(testType)}
                      onChange={() => toggleTestType(testType)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="text-sm text-gray-700">{testType}</span>
                  </label>
                ))}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Detail level:
              </label>
              <Select value={detailLevel} onValueChange={setDetailLevel}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select detail level" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Basic">Basic</SelectItem>
                  <SelectItem value="Detailed">Detailed</SelectItem>
                  <SelectItem value="Comprehensive">Comprehensive</SelectItem>
                </SelectContent>
              </Select>
              <div className="mt-4">
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Case frequency:
                </label>
                <div className="space-y-2">
                  {["Smoke", "Regression"].map((freq) => (
                    <label key={freq} className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={selectedCaseFrequencies.includes(freq)}
                        onChange={() =>
                          setSelectedCaseFrequencies((prev) =>
                            prev.includes(freq)
                              ? prev.filter((f) => f !== freq)
                              : [...prev, freq]
                          )
                        }
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="text-sm text-gray-700">{freq}</span>
                    </label>
                  ))}
                </div>
              </div>
            </div>
          </div>
          
          <Button 
            onClick={generateTestCases}
            disabled={selectedTestTypes.length === 0 || loading || uploading || !requirementsText}
            className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
          >
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                Generating...
              </>
            ) : uploading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                Processing Document...
              </>
            ) : (
              <>
                <CheckCircle className="w-4 h-4 mr-2" />
                Generate Test Cases
              </>
            )}
          </Button>
          {error && <div className="text-red-600 text-sm mt-2">{error}</div>}
        </div>
      )}

      {/* Show generated test cases */}
      {testCases && (
        <div className="bg-white rounded-lg shadow p-6 mt-6 relative">
          <h4 className="text-lg font-bold mb-4 text-blue-800">Generated Test Cases</h4>
          <div className="absolute top-6 right-6 flex gap-2">
            <Button
              onClick={downloadPdf}
              disabled={pdfLoading}
              className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded shadow-none"
            >
              {pdfLoading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                  Downloading PDF...
                </>
              ) : (
                <>
                  <FileDown className="w-4 h-4 mr-2" />
                  Download as PDF
                </>
              )}
            </Button>
            <Button
              onClick={downloadExcel}
              disabled={pdfLoading}
              className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white text-sm rounded shadow-none"
            >
              <FileDown className="w-4 h-4 mr-2" />
              Download as Excel
            </Button>
          </div>
          {testCases.map((tc, idx) => (
            <div key={idx} className="mb-6">
              <h5 className="font-semibold text-gray-900 mb-1">{tc.title || `Test Case ${idx + 1}`}</h5>
              <div className="text-sm text-gray-700 mb-1"><b>Type:</b> {tc.type}</div>
              {tc.description && <div className="text-sm text-gray-700 mb-1"><b>Description:</b> {tc.description}</div>}
              {tc.steps && (
                <div className="text-sm text-gray-700 mb-1">
                  <b>Steps:</b>
                  <ol className="list-decimal ml-5">
                    {tc.steps.map((step: string, i: number) => (
                      <li key={i}>{step}</li>
                    ))}
                  </ol>
                </div>
              )}
              {tc.expected_result && <div className="text-sm text-gray-700"><b>Expected Result:</b> {tc.expected_result}</div>}
              <hr className="my-3" />
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default UploadDocuments;
