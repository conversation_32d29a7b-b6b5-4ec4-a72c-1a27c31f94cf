import React, { useState } from 'react';
import { Send, Sparkles, FileDown } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

const API_BASE = import.meta.env.VITE_API_BASE_URL;

const defaultColumns = [
  "Test Case ID",
  "Type",
  "Case frequency",
  "Test Case Description",
  "Preconditions",
  "Test Steps",
  "Test Data",
  "Expected Result",
  "Actual Result",
  "Status (Pass/Fail)",
  "Comments"
];

const DirectTextInput = () => {
  const [text, setText] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [selectedTestTypes, setSelectedTestTypes] = useState<string[]>(['Functional', 'Integration']);
  const [detailLevel, setDetailLevel] = useState('Detailed');
  const [selectedCaseFrequencies, setSelectedCaseFrequencies] = useState<string[]>(['Smoke']);
  const [error, setError] = useState<string | null>(null);
  const [testCases, setTestCases] = useState<any[] | null>(null);
  const [pdfLoading, setPdfLoading] = useState(false);
  const baseFileName = "Testify";
  const [columns, setColumns] = useState<string[]>([...defaultColumns]);
  const [columnInput, setColumnInput] = useState<string>(defaultColumns.join(", "));

  const testTypeOptions = [
    'Functional',
    'Performance', 
    'Security',
    'Usability',
    'Integration',
    'API'
  ];

  const toggleTestType = (testType: string) => {
    setSelectedTestTypes(prev => 
      prev.includes(testType) 
        ? prev.filter(type => type !== testType)
        : [...prev, testType]
    );
  };

  const generateTestCases = async () => {
    if (!text.trim()) return;
    setIsGenerating(true);
    setError(null);
    setTestCases(null);

    try {
      const formData = new FormData();
      formData.append("requirements_text", text);
      selectedTestTypes.forEach(type => formData.append("test_types", type));
      formData.append("detail_level", detailLevel);
      selectedCaseFrequencies.forEach(freq => formData.append("case_frequency", freq));
      columns.forEach(col => formData.append("columns", col));

      const res = await fetch(`${API_BASE}/generate`, {
        method: "POST",
        body: formData,
      });
      const data = await res.json();
      if (!data.success) {
        throw new Error(data.error || "Failed to generate test cases.");
      }
      setTestCases(data.test_cases);
    } catch (err: any) {
      setError(err.message || "An error occurred.");
    } finally {
      setIsGenerating(false);
    }
  };

  const downloadPdf = async () => {
    if (!testCases || testCases.length === 0) return;
    setPdfLoading(true);
    setError(null);
    try {
      const res = await fetch(`${API_BASE}/export/pdf`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify(testCases)
      });
      if (!res.ok) {
        throw new Error("Failed to generate PDF.");
      }
      const blob = await res.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `Test cases_${baseFileName}.pdf`;
      document.body.appendChild(a);
      a.click();
      a.remove();
      window.URL.revokeObjectURL(url);
    } catch (err: any) {
      setError(err.message || "Failed to download PDF.");
    } finally {
      setPdfLoading(false);
    }
  };

  const downloadExcel = async () => {
    if (!testCases || testCases.length === 0) return;
    setPdfLoading(true);
    setError(null);
    try {
      const res = await fetch(`${API_BASE}/export/excel`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({ test_cases: testCases })
      });
      if (!res.ok) {
        throw new Error("Failed to generate Excel file.");
      }
      const blob = await res.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `Test cases_${baseFileName}.xlsx`;
      document.body.appendChild(a);
      a.click();
      a.remove();
      window.URL.revokeObjectURL(url);
    } catch (err: any) {
      setError(err.message || "Failed to download Excel file.");
    } finally {
      setPdfLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <label htmlFor="text-input" className="block text-sm font-medium text-gray-700 mb-2">
          Enter your PRD/BRD Content
        </label>
        <Textarea
          id="text-input"
          value={text}
          onChange={(e) => setText(e.target.value)}
          placeholder="Paste your Product Requirements Document (PRD) or Business Requirements Document (BRD) content here. Include features, user stories, acceptance criteria, and any specific requirements you want test cases generated for..."
          className="min-h-[300px] resize-none border-gray-300 focus:border-blue-500 focus:ring-blue-500"
        />
        <p className="text-xs text-gray-500 mt-2">
          {text.length} characters
        </p>
      </div>

      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start space-x-3">
          <Sparkles className="h-5 w-5 text-blue-600 mt-0.5" />
          <div>
            <h4 className="text-sm font-medium text-blue-900">Pro Tip</h4>
            <p className="text-sm text-blue-700 mt-1">
              For better test case generation, include user stories, acceptance criteria, 
              edge cases, and specific business rules in your input.
            </p>
          </div>
        </div>
      </div>

      {text.trim() && (
        <>
          {/* Column Selection */}
          <div className="p-4 bg-gray-50 rounded-lg mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Columns to include in the Excel:
            </label>
            <input
              type="text"
              value={columnInput}
              onChange={e => {
                setColumnInput(e.target.value);
                setColumns(
                  e.target.value
                    .split(",")
                    .map(s => s.trim())
                    .filter(s => s.length > 0)
                );
              }}
              className="w-full border border-gray-300 rounded px-3 py-2 text-sm"
              placeholder="Enter column names separated by commas"
            />
            <div className="text-xs text-gray-500 mt-1">
              Default: {defaultColumns.join(", ")}
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 p-4 bg-gray-50 rounded-lg">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Select test types to generate:
              </label>
              <div className="space-y-2">
                {testTypeOptions.map((testType) => (
                  <label key={testType} className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={selectedTestTypes.includes(testType)}
                      onChange={() => toggleTestType(testType)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="text-sm text-gray-700">{testType}</span>
                  </label>
                ))}
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Detail level:
              </label>
              <Select value={detailLevel} onValueChange={setDetailLevel}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select detail level" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Basic">Basic</SelectItem>
                  <SelectItem value="Detailed">Detailed</SelectItem>
                  <SelectItem value="Comprehensive">Comprehensive</SelectItem>
                </SelectContent>
              </Select>
              <div className="mt-4">
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Case frequency:
                </label>
                <div className="space-y-2">
                  {["Smoke", "Regression"].map((freq) => (
                    <label key={freq} className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={selectedCaseFrequencies.includes(freq)}
                        onChange={() =>
                          setSelectedCaseFrequencies((prev) =>
                            prev.includes(freq)
                              ? prev.filter((f) => f !== freq)
                              : [...prev, freq]
                          )
                        }
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="text-sm text-gray-700">{freq}</span>
                    </label>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </>
      )}

      <Button
        onClick={generateTestCases}
        disabled={!text.trim() || isGenerating || selectedTestTypes.length === 0}
        className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 disabled:opacity-50"
      >
        {isGenerating ? (
          <>
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
            Generating Test Cases...
          </>
        ) : (
          <>
            <Send className="w-4 h-4 mr-2" />
            Generate Test Cases
          </>
        )}
      </Button>
      {error && <div className="text-red-600 text-sm mt-2">{error}</div>}

      {/* Show generated test cases */}
      {testCases && (
        <div className="bg-white rounded-lg shadow p-6 mt-6 relative">
          <h4 className="text-lg font-bold mb-4 text-blue-800">Generated Test Cases</h4>
          <div className="absolute top-6 right-6 flex gap-2">
            <Button
              onClick={downloadPdf}
              disabled={pdfLoading}
              className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded shadow-none"
            >
              {pdfLoading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                  Downloading PDF...
                </>
              ) : (
                <>
                  <FileDown className="w-4 h-4 mr-2" />
                  Download as PDF
                </>
              )}
            </Button>
            <Button
              onClick={downloadExcel}
              disabled={pdfLoading}
              className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white text-sm rounded shadow-none"
            >
              <FileDown className="w-4 h-4 mr-2" />
              Download as Excel
            </Button>
          </div>
          {testCases.map((tc, idx) => (
            <div key={idx} className="mb-6">
              <h5 className="font-semibold text-gray-900 mb-1">{tc.title || `Test Case ${idx + 1}`}</h5>
              <div className="text-sm text-gray-700 mb-1"><b>Type:</b> {tc.type}</div>
              {tc.description && <div className="text-sm text-gray-700 mb-1"><b>Description:</b> {tc.description}</div>}
              {tc.steps && (
                <div className="text-sm text-gray-700 mb-1">
                  <b>Steps:</b>
                  <ol className="list-decimal ml-5">
                    {tc.steps.map((step: string, i: number) => (
                      <li key={i}>{step}</li>
                    ))}
                  </ol>
                </div>
              )}
              {tc.expected_result && <div className="text-sm text-gray-700"><b>Expected Result:</b> {tc.expected_result}</div>}
              <hr className="my-3" />
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default DirectTextInput;
