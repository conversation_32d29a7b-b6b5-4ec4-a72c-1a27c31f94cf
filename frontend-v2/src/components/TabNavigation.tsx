
import React from 'react';
import { Upload, FileText } from 'lucide-react';

interface TabNavigationProps {
  activeTab: 'upload' | 'text';
  onTabChange: (tab: 'upload' | 'text') => void;
}

const TabNavigation = ({ activeTab, onTabChange }: TabNavigationProps) => {
  return (
    <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
      <button
        onClick={() => onTabChange('upload')}
        className={`flex items-center space-x-2 px-4 py-2 rounded-md transition-all duration-200 ${
          activeTab === 'upload'
            ? 'bg-white text-blue-600 shadow-sm'
            : 'text-gray-600 hover:text-gray-800'
        }`}
      >
        <Upload className="w-4 h-4" />
        <span className="font-medium">Upload Documents</span>
      </button>
      <button
        onClick={() => onTabChange('text')}
        className={`flex items-center space-x-2 px-4 py-2 rounded-md transition-all duration-200 ${
          activeTab === 'text'
            ? 'bg-white text-blue-600 shadow-sm'
            : 'text-gray-600 hover:text-gray-800'
        }`}
      >
        <FileText className="w-4 h-4" />
        <span className="font-medium">Direct Text Input</span>
      </button>
    </div>
  );
};

export default TabNavigation;
