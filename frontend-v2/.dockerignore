# Dependencies
node_modules

# Build outputs
dist
build

# Version control
.git
.gitignore

# IDE files
.vscode
.idea

# Log files
npm-debug.log*
yarn-debug.log*
yarn-error.log*
logs
*.log

# Environment files
.env
.env.*

# Testing
coverage
.nyc_output

# Package manager files
bun.lockb
.pnpm-debug.log*

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp

# Docker
Dockerfile
.dockerignore

# Documentation
README.md
*.md
