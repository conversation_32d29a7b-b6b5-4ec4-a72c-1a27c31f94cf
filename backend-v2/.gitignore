# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
env/
ENV/
.env
.venv
env.bak/
venv.bak/

# IDE specific files
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# Project specific
*.log
.coverage
htmlcov/
.pytest_cache/
.tox/
outputs/
uploads/
vector_db/

# Database files
*.db
*.sqlite
*.sqlite3

# Chroma vector store
.chroma/
chromadb/

# Documentation
docs/_build/
site/

# Jupyter Notebook
.ipynb_checkpoints
*.ipynb

# Distribution / packaging
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
