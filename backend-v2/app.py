from fastapi import <PERSON><PERSON><PERSON>, File, UploadFile, Form, Body
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, StreamingResponse
from typing import List, Optional
import os
import tempfile
import io

from document_processor import DocumentProcessor
from test_case_agent import TestCaseGeneratorSimple
from vector_store import VectorStoreManager
from langchain.schema import Document
from fpdf import FPDF

app = FastAPI(
    title="Testify API",
    description="API for AI-powered test case generation from requirements documents.",
    version="1.0.0"
)

# CORS setup
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Change to your frontend domain in production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.post("/upload")
async def upload_document(file: UploadFile = File(...)):
    """
    Accepts a requirements document, processes it, and returns extracted text.
    """
    try:
        doc_processor = DocumentProcessor()
        with tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(file.filename)[-1]) as tmp_file:
            tmp_file.write(await file.read())
            tmp_file_path = tmp_file.name

        documents = doc_processor.process_document(tmp_file_path)
        requirements_text = "\n\n".join([doc.page_content for doc in documents])

        os.unlink(tmp_file_path)
        return {"success": True, "requirements_text": requirements_text}
    except Exception as e:
        return JSONResponse(status_code=400, content={"success": False, "error": str(e)})

@app.post("/generate")
async def generate_test_cases(
    requirements_text: str = Form(...),
    test_types: List[str] = Form(...),
    detail_level: str = Form(...),
    case_frequency: List[str] = Form(None),
    columns: List[str] = Form(None)
):
    """
    Generates test cases from requirements text, test types, detail level, and user-selected case frequency.
    Also supports custom columns, generating data for them using the LLM.
    """
    try:
        generator = TestCaseGeneratorSimple()
        all_test_cases = []
        # If multiple case frequencies are selected, generate test cases for each frequency separately
        if case_frequency and len(case_frequency) > 0:
            for freq in case_frequency:
                context = {
                    "test_types": test_types,
                    "case_frequencies": [freq],
                    "detail_level": detail_level,
                }
                result = generator.generate_test_cases_with_context(requirements_text, context)
                if result and "test_cases" in result:
                    for tc in result["test_cases"]:
                        tc["case_frequency"] = freq
                    all_test_cases.extend(result["test_cases"])
        else:
            context = {
                "test_types": test_types,
                "case_frequencies": [],
                "detail_level": detail_level,
            }
            result = generator.generate_test_cases_with_context(requirements_text, context)
            if result and "test_cases" in result:
                all_test_cases.extend(result["test_cases"])

        # Assign global sequential IDs to all test cases
        for idx, tc in enumerate(all_test_cases, 1):
            tc["id"] = f"TC-{idx}"

        # Handle custom columns
        default_columns = [
            "Test Case ID",
            "Type",
            "Case frequency",
            "Test Case Description",
            "Preconditions",
            "Test Steps",
            "Test Data",
            "Expected Result",
            "Actual Result",
            "Status (Pass/Fail)",
            "Comments"
        ]
        custom_columns = []
        if columns:
            custom_columns = [col for col in columns if col not in default_columns]
            # For each test case, generate data for each custom column
            for tc in all_test_cases:
                for col in custom_columns:
                    # TODO: Replace this with actual LLM call to generate value for (col, tc)
                    tc[col] = f"Generated by LLM for {col}"

        # Embed the requirements text in the vector store after generating test cases
        try:
            vs_manager = VectorStoreManager()
            if not vs_manager.load_vector_store():
                vs_manager.create_vector_store([])
            doc_obj = Document(page_content=requirements_text, metadata={"source": "generate_api"})
            vs_manager.add_documents([doc_obj])
        except Exception as embed_err:
            import logging
            logging.warning(f"Vector store embedding error: {embed_err}")
        return {"success": True, "test_cases": all_test_cases}
    except Exception as e:
        return JSONResponse(status_code=400, content={"success": False, "error": str(e)})

@app.get("/")
def root():
    return {"message": "Testify FastAPI backend is running."}

@app.post("/export/pdf")
async def export_pdf(test_cases: List[dict] = Body(...)):
    """
    Accepts a list of test cases and returns a generated PDF file.
    """
    try:
        pdf = FPDF()
        pdf.add_page()
        pdf.set_font("Arial", size=12)
        pdf.cell(0, 10, "Test Cases", ln=True, align="C")
        pdf.ln(5)
        for i, tc in enumerate(test_cases, 1):
            pdf.set_font("Arial", "B", 12)
            title = tc.get("title", f"Test Case {i}")
            pdf.cell(0, 10, f"Test Case {i}: {title}", ln=True)
            pdf.set_font("Arial", "", 11)
            pdf.cell(0, 8, f"Type: {tc.get('type', '')}", ln=True)
            if "description" in tc:
                pdf.multi_cell(0, 8, f"Description: {tc['description']}")
            if "steps" in tc:
                steps = tc["steps"]
                if isinstance(steps, list):
                    steps_str = "\n".join([f"{j+1}. {step}" for j, step in enumerate(steps)])
                else:
                    steps_str = str(steps)
                pdf.multi_cell(0, 8, "Steps:\n" + steps_str)
            if "expected_result" in tc:
                pdf.multi_cell(0, 8, f"Expected Result: {tc['expected_result']}")
            pdf.ln(4)
        pdf_bytes = pdf.output(dest='S').encode('latin1')
        return StreamingResponse(io.BytesIO(pdf_bytes), media_type="application/pdf", headers={
            "Content-Disposition": "attachment; filename=test_cases.pdf"
        })
    except Exception as e:
        return JSONResponse(status_code=400, content={"success": False, "error": str(e)})

import csv

from pydantic import BaseModel

class TestCasesRequest(BaseModel):
    test_cases: list[dict]

@app.post("/export/csv")
async def export_csv(request: TestCasesRequest):
    """
    Accepts a list of test cases and returns a generated CSV file.
    """
    try:
        output = io.StringIO()
        writer = csv.writer(output)
        # Write header
        writer.writerow(["S.NO", "Title", "Type", "Steps", "Expected Result"])
        # Write test cases
        for i, tc in enumerate(request.test_cases, 1):
            title = tc.get("title", f"Test Case {i}")
            tc_type = tc.get("type", "")
            steps = tc.get("steps", [])
            if isinstance(steps, list):
                steps_str = "\n".join([f"{j+1}. {step}" for j, step in enumerate(steps)])
            else:
                steps_str = str(steps)
            expected_result = tc.get("expected_result", "")
            writer.writerow([i, title, tc_type, steps_str, expected_result])
        output.seek(0)
        return StreamingResponse(io.BytesIO(output.getvalue().encode("utf-8")), media_type="text/csv", headers={
            "Content-Disposition": "attachment; filename=test_cases.csv"
        })
    except Exception as e:
        return JSONResponse(status_code=400, content={"success": False, "error": str(e)})

import pandas as pd

@app.post("/export/excel")
async def export_excel(request: TestCasesRequest):
    """
    Accepts a list of test cases and returns a generated Excel (.xlsx) file.
    Supports custom columns.
    """
    try:
        # Determine all columns (default + custom) in order
        default_columns = [
            "Test Case ID",
            "Type",
            "Case frequency",
            "Test Case Description",
            "Preconditions",
            "Test Steps",
            "Test Data",
            "Expected Result",
            "Actual Result",
            "Status (Pass/Fail)",
            "Comments"
        ]
        # Collect all columns from test cases
        all_keys = set()
        for tc in request.test_cases:
            all_keys.update(tc.keys())
        # Preserve order: default columns first, then custom columns in order of appearance
        custom_columns = [k for k in all_keys if k not in default_columns]
        columns = default_columns + [col for col in custom_columns if col not in default_columns]

        # Prepare data for DataFrame
        data = []
        for i, tc in enumerate(request.test_cases, 1):
            row = {}
            # Fill default columns
            row["Test Case ID"] = tc.get("id", f"TC-{i}")
            row["Type"] = tc.get("type", "")
            case_frequency = tc.get("case_frequency", "")
            if isinstance(case_frequency, list):
                case_frequency_str = ", ".join([str(cf) for cf in case_frequency])
            else:
                case_frequency_str = str(case_frequency)
            row["Case frequency"] = case_frequency_str
            title = tc.get("title", "")
            description = tc.get("description", f"Test Case {i}")
            import re
            description_clean = description
            if title:
                description_clean = re.sub(r"^[^:]+:\s*", "", title)
            row["Test Case Description"] = description_clean
            row["Preconditions"] = tc.get("preconditions", "")
            steps = tc.get("steps", [])
            if isinstance(steps, list):
                steps_str = "\n".join([f"{j+1}. {step}" for j, step in enumerate(steps)])
            else:
                steps_str = str(steps)
            row["Test Steps"] = steps_str
            row["Test Data"] = tc.get("test_data", "")
            row["Expected Result"] = tc.get("expected_result", "")
            row["Actual Result"] = tc.get("Actual Result", "")
            row["Status (Pass/Fail)"] = tc.get("Status (Pass/Fail)", "")
            row["Comments"] = tc.get("Comments", "")
            # Fill custom columns
            for col in custom_columns:
                row[col] = tc.get(col, "")
            data.append(row)

        df = pd.DataFrame(data, columns=columns)
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine="openpyxl") as writer:
            df.to_excel(writer, index=False, sheet_name="Test Cases")
            workbook = writer.book
            worksheet = writer.sheets["Test Cases"]

            from openpyxl.styles import Font, Alignment, PatternFill, Border, Side

            # Header formatting
            header_font = Font(bold=True)
            header_fill = PatternFill("solid", fgColor="D9E1F2")  # Light blue
            for cell in worksheet[1]:
                cell.font = header_font
                cell.fill = header_fill

            # Freeze header row
            worksheet.freeze_panes = worksheet["A2"]

            # Text wrapping and borders
            thin_border = Border(left=Side(style='thin'), right=Side(style='thin'),
                                 top=Side(style='thin'), bottom=Side(style='thin'))
            for row in worksheet.iter_rows():
                for cell in row:
                    cell.alignment = Alignment(wrap_text=True, vertical="top")
                    cell.border = thin_border

            # Auto-fit column widths
            for column_cells in worksheet.columns:
                length = max(len(str(cell.value) if cell.value else "") for cell in column_cells)
                worksheet.column_dimensions[column_cells[0].column_letter].width = length + 2

        output.seek(0)
        return StreamingResponse(output, media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", headers={
            "Content-Disposition": "attachment; filename=test_cases.xlsx"
        })
    except Exception as e:
        return JSONResponse(status_code=400, content={"success": False, "error": str(e)})
