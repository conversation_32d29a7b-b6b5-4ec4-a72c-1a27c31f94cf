#!/usr/bin/env python3
"""
Simplified Test Case Agent using Gemini AI without vector storage dependencies
"""

from typing import List, Dict, Any
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain.prompts import ChatPromptTemplate
from langchain.schema import HumanMessage, AIMessage
from config import Config
import json

class TestCaseGeneratorSimple:
    def __init__(self):
        """Initialize the test case generator with Gemini"""
        self.llm = ChatGoogleGenerativeAI(
            model=Config.LLM_MODEL,
            temperature=Config.TEMPERATURE
        )
        
    def generate_test_cases(self, requirements_text: str) -> Dict[str, Any]:
        """Generate test cases from requirements text"""
        
        prompt = ChatPromptTemplate.from_template("""
        You are an expert QA engineer. Generate comprehensive test cases from the given requirements.

        Requirements:
        {requirements}

        Generate test cases following this JSON structure:
        {{
            "summary": {{
                "total_test_cases": number,
                "functional_cases": number,
                "non_functional_cases": number,
                "edge_cases": number
            }},
            "test_cases": [
                {{
                    "id": "TC_001",
                    "title": "Test case title",
                    "type": "Functional/Performance/Security/etc",
                    "priority": "High/Medium/Low",
                    "description": "Brief description",
                    "preconditions": "Required setup",
                    "steps": ["<step 1>", "<step 2>", "<step 3>"],
                    "expected_result": "Expected outcome"
                }}
            ]
        }}

        Requirements for test case generation:
        1. Create at least 5-10 test cases
        2. Include different types: Functional, Integration, Edge cases
        3. Cover positive and negative scenarios
        4. Include appropriate priority levels
        5. Make steps clear and actionable
        6. Ensure expected results are specific
        7. IMPORTANT: Steps should be plain sentences, do NOT number them or prefix with "Step". Do not use hardcoded examples; generate steps based only on the requirements provided.

        Generate comprehensive test cases covering all aspects of the requirements.
        """)
        
        try:
            # Check API key first
            if not Config.GOOGLE_API_KEY or Config.GOOGLE_API_KEY == 'your_google_api_key_here':
                return {
                    "error": "Google API key not configured. Please set GOOGLE_API_KEY in your .env file."
                }
            
            # Generate response
            chain = prompt | self.llm
            response = chain.invoke({"requirements": requirements_text})
            
            # Parse the response
            content = response.content
            
            # Try to extract JSON from the response
            try:
                # Look for JSON in the response
                start_idx = content.find('{')
                end_idx = content.rfind('}') + 1
                
                if start_idx != -1 and end_idx > start_idx:
                    json_str = content[start_idx:end_idx]
                    result = json.loads(json_str)
                    return result
                else:
                    # If no JSON found, create a basic structure
                    return {
                        "error": "Could not parse JSON response from AI",
                        "raw_response": content
                    }
                    
            except json.JSONDecodeError as e:
                return {
                    "error": f"JSON parsing error: {str(e)}",
                    "raw_response": content
                }
                
        except Exception as e:
            return {
                "error": f"Failed to generate test cases: {str(e)}"
            }
    
    def generate_test_cases_with_context(self, requirements_text: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Generate test cases with additional context about file type and preferences"""
        
        # Build context-aware prompt
        test_types_str = ", ".join(context.get("test_types", ["Functional"]))
        detail_level = context.get("detail_level", "Detailed")
        source_file = context.get("source_file", "unknown")
        file_type = context.get("file_type", "text")
        case_frequencies = context.get("case_frequencies", ["Smoke"])
        case_frequencies_str = ", ".join(case_frequencies)
        case_frequencies_list = '", "'.join(case_frequencies)
        case_frequencies_list = f'"{case_frequencies_list}"'

        prompt = ChatPromptTemplate.from_template("""
        You are an expert QA engineer. Generate comprehensive test cases from the given requirements.

        CONTEXT INFORMATION:
        - Source: {source_file}
        - File Type: {file_type}
        - Requested Test Types: {test_types}
        - Requested Case Frequencies: {case_frequencies}
        - Detail Level: {detail_level}

        Requirements Document:
        {requirements}

        For each combination of requested test type and case frequency (e.g., Functional/Smoke, Functional/Regression, etc.), generate SEPARATE test cases. Steps and expected results may differ for each frequency. For every combination, generate both positive and negative test cases.

        Generate test cases following this JSON structure:
        {{
            "metadata": {{
                "source_type": "{file_type}",
                "test_types_requested": [{test_types_list}],
                "case_frequencies_requested": [{case_frequencies_list}],
                "detail_level": "{detail_level}",
                "generation_timestamp": "auto-generated"
            }},
            "summary": {{
                "total_test_cases": number,
                "test_type_breakdown": {{"Functional": count, "Integration": count, etc}}
            }},
            "test_cases": [
                {{
                    "id": "TC_001",
                    "title": "Clear, specific test case title",
                    "type": "One of: {test_types}",
                    "case_frequency": "One of: {case_frequencies}",
                    "priority": "High/Medium/Low",
                    "description": "Brief but comprehensive description",
                    "preconditions": "Required setup and prerequisites", 
                    "steps": ["<step 1>", "<step 2>", "<step 3>"],
                    "expected_result": "Specific, measurable expected outcome",
                    "test_data": "Sample data if applicable"
                }}
            ]
        }}

        GENERATION GUIDELINES:
        1. For every combination of test type and case frequency, generate at least one positive and one negative test case.
        2. Focus primarily on the requested test types: {test_types}
        3. Adjust detail level based on: {detail_level}
           - Basic: 3-5 test cases, simple steps
           - Detailed: 5-8 test cases, comprehensive steps  
           - Comprehensive: up to 30 test cases, extensive coverage (do not generate more than 30 test cases)
        4. Consider the source file type ({file_type}) when creating tests:
           - For structured data (JSON/CSV/Excel): Include data validation tests
           - For documents (PDF/Word): Focus on business logic tests
           - For code/config files: Include technical validation tests
           - For web content (HTML/XML): Include format and structure tests
        5. Ensure test steps are actionable and specific
        6. Make expected results measurable and verifiable
        7. Add appropriate test data examples where relevant
        8. IMPORTANT: Steps should be plain sentences, do NOT number them or prefix with "Step". Do not use hardcoded examples; generate steps based only on the requirements provided.
        9. CRITICAL: Output a complete, valid JSON array of test cases. Do NOT use ellipsis (...), comments, or incomplete arrays. Do NOT summarize or skip test cases. The output must be valid JSON that can be parsed without errors.

        Generate test cases that thoroughly cover the requirements with the specified focus areas.
        """)
        
        try:
            # Check API key first
            if not Config.GOOGLE_API_KEY or Config.GOOGLE_API_KEY == 'your_google_api_key_here':
                return {
                    "error": "Google API key not configured. Please set GOOGLE_API_KEY in your .env file."
                }
            
            # Prepare test types for JSON format
            test_types_list = '", "'.join(context.get("test_types", ["Functional"]))
            test_types_list = f'"{test_types_list}"'
            
            # Generate response
            chain = prompt | self.llm
            response = chain.invoke({
                "requirements": requirements_text,
                "source_file": source_file,
                "file_type": file_type,
                "test_types": test_types_str,
                "test_types_list": test_types_list,
                "case_frequencies": case_frequencies_str,
                "case_frequencies_list": case_frequencies_list,
                "detail_level": detail_level
            })
            
            # Parse the response
            content = response.content

            # DEBUG LOGGING: Print raw LLM response and context
            print("=== LLM RAW RESPONSE START ===")
            print(content)
            print("=== LLM RAW RESPONSE END ===")
            print("=== LLM CONTEXT ===")
            print({
                "requirements": requirements_text,
                "source_file": source_file,
                "file_type": file_type,
                "test_types": test_types_str,
                "test_types_list": test_types_list,
                "case_frequencies": case_frequencies_str,
                "case_frequencies_list": case_frequencies_list,
                "detail_level": detail_level
            })

            # Try to extract JSON from the response
            try:
                # Look for JSON in the response
                start_idx = content.find('{')
                end_idx = content.rfind('}') + 1

                if start_idx != -1 and end_idx > start_idx:
                    json_str = content[start_idx:end_idx]
                    result = json.loads(json_str)

                    # Add timestamp if not present
                    if "metadata" in result and "generation_timestamp" in result["metadata"]:
                        from datetime import datetime
                        result["metadata"]["generation_timestamp"] = datetime.now().isoformat()

                    return result
                else:
                    # If no JSON found, create a fallback structure
                    print("=== LLM FALLBACK: No JSON found in response ===")
                    return self._create_fallback_response(content, context)

            except json.JSONDecodeError as e:
                print(f"=== LLM FALLBACK: JSON parsing error: {str(e)} ===")
                return self._create_fallback_response(content, context, f"JSON parsing error: {str(e)}")

        except Exception as e:
            print(f"=== LLM EXCEPTION: {str(e)} ===")
            return {
                "error": f"Failed to generate test cases: {str(e)}"
            }
    
    def _create_fallback_response(self, content: str, context: Dict[str, Any], error_msg: str = None) -> Dict[str, Any]:
        """Create a fallback response when JSON parsing fails"""
        from datetime import datetime
        
        # Try to extract test cases from the content manually
        test_cases = []
        lines = content.split('\n')
        
        current_case = {}
        for line in lines:
            line = line.strip()
            if line.startswith('TC_') or line.startswith('Test Case'):
                if current_case:
                    test_cases.append(current_case)
                current_case = {
                    "id": f"TC_{len(test_cases)+1:03d}",
                    "title": line,
                    "type": context.get("test_types", ["Functional"])[0],
                    "priority": "Medium",
                    "case_frequency": "Smoke",  # Default/fallback
                    "description": "Generated from AI response",
                    "steps": [],
                    "expected_result": "To be verified"
                }
            elif line and current_case:
                if not current_case.get("description"):
                    current_case["description"] = line
                else:
                    import re
                    # Remove leading numbers or "Step X" from the step
                    clean_line = re.sub(r"^(Step\s*\d+\.?\s*|\d+\.\s*)", "", line, flags=re.IGNORECASE).strip()
                    current_case["steps"].append(clean_line)
        
        if current_case:
            test_cases.append(current_case)
        
        # If no test cases found, create a minimal one
        if not test_cases:
            test_cases = [{
                "id": "TC_001",
                "title": "Basic Functional Test",
                "type": context.get("test_types", ["Functional"])[0],
                "priority": "High",
                "case_frequency": "Smoke",
                "description": "Basic test case generated from requirements",
                "steps": ["Review requirements", "Execute test", "Verify results"],
                "expected_result": "System behaves as specified"
            }]
        
        return {
            "metadata": {
                "source_type": context.get("file_type", "text"),
                "test_types_requested": context.get("test_types", ["Functional"]),
                "detail_level": context.get("detail_level", "Basic"),
                "generation_timestamp": datetime.now().isoformat(),
                "fallback_response": True,
                "parse_error": error_msg
            },
            "summary": {
                "total_test_cases": len(test_cases),
                "test_type_breakdown": {tc["type"]: 1 for tc in test_cases}
            },
            "test_cases": test_cases,
            "raw_response": content
        }

# Test the generator
if __name__ == "__main__":
    generator = TestCaseGeneratorSimple()
    
    sample_requirements = """
    User Authentication System:
    - Users must be able to register with email and password
    - Password must be at least 8 characters with special characters
    - Users can log in with valid credentials
    - Account gets locked after 3 failed login attempts
    - Users can reset password via email
    """
    
    print("Testing Gemini Test Case Generator...")
    result = generator.generate_test_cases(sample_requirements)
    
    if "error" in result:
        print(f"Error: {result['error']}")
    else:
        print(f"Generated {result.get('summary', {}).get('total_test_cases', 0)} test cases")
        
    print("✅ Test completed!")
