import os
import PyPDF2
import docx
import json
import yaml
import csv
import xml.etree.ElementTree as ET
from typing import List, Dict, Any, Union
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.schema import Document
from config import Config
import re

# Optional imports for additional file types
try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False

try:
    from pptx import Presentation
    PPTX_AVAILABLE = True
except ImportError:
    PPTX_AVAILABLE = False

try:
    from bs4 import BeautifulSoup
    BS4_AVAILABLE = True
except ImportError:
    BS4_AVAILABLE = False

try:
    import openpyxl
    OPENPYXL_AVAILABLE = True
except ImportError:
    OPENPYXL_AVAILABLE = False

class DocumentProcessor:
    def __init__(self):
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=Config.CHUNK_SIZE,
            chunk_overlap=Config.CHUNK_OVERLAP,
            separators=["\n\n", "\n", ".", "!", "?", ",", " ", ""]
        )
        
        # Supported file formats
        self.supported_formats = {
            '.pdf': self.extract_text_from_pdf,
            '.docx': self.extract_text_from_docx,
            '.doc': self.extract_text_from_docx,  # Will try with docx library
            '.txt': self.extract_text_from_txt,
            '.md': self.extract_text_from_markdown,
            '.markdown': self.extract_text_from_markdown,
            '.json': self.extract_text_from_json,
            '.yaml': self.extract_text_from_yaml,
            '.yml': self.extract_text_from_yaml,
            '.csv': self.extract_text_from_csv,
            '.tsv': self.extract_text_from_csv,
            '.html': self.extract_text_from_html,
            '.htm': self.extract_text_from_html,
            '.xml': self.extract_text_from_xml,
            '.rtf': self.extract_text_from_rtf,
            '.log': self.extract_text_from_txt,
            '.cfg': self.extract_text_from_txt,
            '.conf': self.extract_text_from_txt,
            '.ini': self.extract_text_from_txt,
        }
        
        # Add Excel and PowerPoint if libraries are available
        if PANDAS_AVAILABLE:
            self.supported_formats.update({
                '.xlsx': self.extract_text_from_excel,
                '.xls': self.extract_text_from_excel,
            })
        
        if PPTX_AVAILABLE:
            self.supported_formats.update({
                '.pptx': self.extract_text_from_pptx,
            })
    
    def get_supported_extensions(self) -> List[str]:
        """Get list of supported file extensions."""
        return list(self.supported_formats.keys())
    
    def extract_text_from_pdf(self, file_path: str) -> str:
        """Extract text from PDF file."""
        text = ""
        try:
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                for page in pdf_reader.pages:
                    text += page.extract_text() + "\n"
        except Exception as e:
            print(f"Error reading PDF {file_path}: {str(e)}")
        return text
    
    def extract_text_from_docx(self, file_path: str) -> str:
        """Extract text from DOCX/DOC file."""
        text = ""
        try:
            doc = docx.Document(file_path)
            for paragraph in doc.paragraphs:
                text += paragraph.text + "\n"
            
            # Extract text from tables
            for table in doc.tables:
                for row in table.rows:
                    for cell in row.cells:
                        text += cell.text + "\t"
                    text += "\n"
        except Exception as e:
            print(f"Error reading DOCX {file_path}: {str(e)}")
        return text
    
    def extract_text_from_txt(self, file_path: str) -> str:
        """Extract text from TXT file."""
        text = ""
        encodings = ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1']
        
        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as file:
                    text = file.read()
                break
            except UnicodeDecodeError:
                continue
            except Exception as e:
                print(f"Error reading TXT {file_path}: {str(e)}")
                break
        return text
    
    def extract_text_from_markdown(self, file_path: str) -> str:
        """Extract text from Markdown file."""
        text = self.extract_text_from_txt(file_path)
        # Basic markdown cleanup
        text = re.sub(r'#{1,6}\s+', '', text)  # Remove headers
        text = re.sub(r'\*\*(.*?)\*\*', r'\1', text)  # Remove bold
        text = re.sub(r'\*(.*?)\*', r'\1', text)  # Remove italic
        text = re.sub(r'`(.*?)`', r'\1', text)  # Remove inline code
        text = re.sub(r'```.*?```', '', text, flags=re.DOTALL)  # Remove code blocks
        return text
    
    def extract_text_from_json(self, file_path: str) -> str:
        """Extract text from JSON file."""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                data = json.load(file)
            return self._json_to_text(data)
        except Exception as e:
            print(f"Error reading JSON {file_path}: {str(e)}")
            return ""
    
    def extract_text_from_yaml(self, file_path: str) -> str:
        """Extract text from YAML file."""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                data = yaml.safe_load(file)
            return self._dict_to_text(data)
        except Exception as e:
            print(f"Error reading YAML {file_path}: {str(e)}")
            return ""
    
    def extract_text_from_csv(self, file_path: str) -> str:
        """Extract text from CSV/TSV file."""
        text = ""
        delimiter = '\t' if file_path.endswith('.tsv') else ','
        
        try:
            with open(file_path, 'r', encoding='utf-8', newline='') as file:
                reader = csv.reader(file, delimiter=delimiter)
                for row in reader:
                    text += " | ".join(row) + "\n"
        except Exception as e:
            print(f"Error reading CSV {file_path}: {str(e)}")
        return text
    
    def extract_text_from_excel(self, file_path: str) -> str:
        """Extract text from Excel file."""
        if not PANDAS_AVAILABLE:
            return "Pandas not available for Excel processing"
        
        text = ""
        try:
            # Read all sheets
            excel_file = pd.ExcelFile(file_path)
            for sheet_name in excel_file.sheet_names:
                df = pd.read_excel(file_path, sheet_name=sheet_name)
                text += f"\n=== Sheet: {sheet_name} ===\n"
                text += df.to_string(index=False) + "\n"
        except Exception as e:
            print(f"Error reading Excel {file_path}: {str(e)}")
        return text
    
    def extract_text_from_pptx(self, file_path: str) -> str:
        """Extract text from PowerPoint file."""
        if not PPTX_AVAILABLE:
            return "python-pptx not available for PowerPoint processing"
        
        text = ""
        try:
            prs = Presentation(file_path)
            for i, slide in enumerate(prs.slides):
                text += f"\n=== Slide {i+1} ===\n"
                for shape in slide.shapes:
                    if hasattr(shape, "text"):
                        text += shape.text + "\n"
        except Exception as e:
            print(f"Error reading PPTX {file_path}: {str(e)}")
        return text
    
    def extract_text_from_html(self, file_path: str) -> str:
        """Extract text from HTML file."""
        text = ""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                html_content = file.read()
            
            if BS4_AVAILABLE:
                soup = BeautifulSoup(html_content, 'html.parser')
                text = soup.get_text(separator='\n', strip=True)
            else:
                # Basic HTML tag removal
                text = re.sub(r'<[^>]+>', '', html_content)
                text = re.sub(r'\s+', ' ', text).strip()
        except Exception as e:
            print(f"Error reading HTML {file_path}: {str(e)}")
        return text
    
    def extract_text_from_xml(self, file_path: str) -> str:
        """Extract text from XML file."""
        text = ""
        try:
            tree = ET.parse(file_path)
            root = tree.getroot()
            text = self._xml_to_text(root)
        except Exception as e:
            print(f"Error reading XML {file_path}: {str(e)}")
        return text
    
    def extract_text_from_rtf(self, file_path: str) -> str:
        """Extract text from RTF file (basic implementation)."""
        text = ""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                rtf_content = file.read()
            # Basic RTF tag removal
            text = re.sub(r'\\[a-z]+\d*\s?', '', rtf_content)
            text = re.sub(r'[{}]', '', text)
            text = re.sub(r'\s+', ' ', text).strip()
        except Exception as e:
            print(f"Error reading RTF {file_path}: {str(e)}")
        return text
    
    def _json_to_text(self, data: Any, indent: int = 0) -> str:
        """Convert JSON data to readable text."""
        text = ""
        prefix = "  " * indent
        
        if isinstance(data, dict):
            for key, value in data.items():
                text += f"{prefix}{key}: "
                if isinstance(value, (dict, list)):
                    text += "\n" + self._json_to_text(value, indent + 1)
                else:
                    text += f"{value}\n"
        elif isinstance(data, list):
            for i, item in enumerate(data):
                text += f"{prefix}[{i}]: "
                if isinstance(item, (dict, list)):
                    text += "\n" + self._json_to_text(item, indent + 1)
                else:
                    text += f"{item}\n"
        else:
            text += f"{prefix}{data}\n"
        
        return text
    
    def _dict_to_text(self, data: Any, indent: int = 0) -> str:
        """Convert dictionary data to readable text."""
        return self._json_to_text(data, indent)
    
    def _xml_to_text(self, element: ET.Element, indent: int = 0) -> str:
        """Convert XML element to readable text."""
        text = ""
        prefix = "  " * indent
        
        if element.text and element.text.strip():
            text += f"{prefix}{element.tag}: {element.text.strip()}\n"
        else:
            text += f"{prefix}{element.tag}:\n"
        
        for child in element:
            text += self._xml_to_text(child, indent + 1)
        
        return text
    
    def process_document(self, file_path: str) -> List[Document]:
        """Process a document and return chunks as Document objects."""
        file_extension = os.path.splitext(file_path)[1].lower()
        
        if file_extension not in self.supported_formats:
            raise ValueError(f"Unsupported file format: {file_extension}. Supported formats: {', '.join(self.get_supported_extensions())}")
        
        # Extract text using appropriate method
        text = self.supported_formats[file_extension](file_path)
        
        if not text.strip():
            raise ValueError(f"No text could be extracted from {file_path}")
        
        # Split text into chunks
        chunks = self.text_splitter.split_text(text)
        
        # Create Document objects
        documents = []
        for i, chunk in enumerate(chunks):
            doc = Document(
                page_content=chunk,
                metadata={
                    "source": file_path,
                    "file_type": file_extension,
                    "chunk_id": i,
                    "total_chunks": len(chunks)
                }
            )
            documents.append(doc)
        
        return documents
    
    def process_text_directly(self, text: str, source_name: str = "direct_input") -> List[Document]:
        """Process text directly without file upload."""
        if not text.strip():
            raise ValueError("No text provided")
        
        # Split text into chunks
        chunks = self.text_splitter.split_text(text)
        
        # Create Document objects
        documents = []
        for i, chunk in enumerate(chunks):
            doc = Document(
                page_content=chunk,
                metadata={
                    "source": source_name,
                    "file_type": "text",
                    "chunk_id": i,
                    "total_chunks": len(chunks)
                }
            )
            documents.append(doc)
        
        return documents 