import os
import chromadb
from typing import List, Optional
from langchain_chroma import Chroma
from langchain_huggingface import HuggingFaceEmbeddings
from langchain.schema import Document
from config import Config

class VectorStoreManager:
    def __init__(self):
        self.embeddings = HuggingFaceEmbeddings(
            model_name=Config.EMBEDDING_MODEL,
            model_kwargs={'device': 'cpu'}
        )
        self.vector_store = None
        self.collection_name = "requirements_documents"
        
    def create_vector_store(self, documents: List[Document]) -> Chroma:
        """Create a new vector store from documents."""
        # Ensure the vector db directory exists
        os.makedirs(Config.VECTOR_DB_PATH, exist_ok=True)
        
        self.vector_store = Chroma.from_documents(
            documents=documents,
            embedding=self.embeddings,
            persist_directory=Config.VECTOR_DB_PATH,
            collection_name=self.collection_name
        )
        
        # Note: Persistence is automatic in ChromaDB 0.4.x+
        return self.vector_store
    
    def load_vector_store(self) -> Optional[Chroma]:
        """Load existing vector store."""
        if os.path.exists(Config.VECTOR_DB_PATH):
            try:
                self.vector_store = Chroma(
                    persist_directory=Config.VECTOR_DB_PATH,
                    embedding_function=self.embeddings,
                    collection_name=self.collection_name
                )
                return self.vector_store
            except Exception as e:
                print(f"Error loading vector store: {str(e)}")
                return None
        return None
    
    def add_documents(self, documents: List[Document]):
        """Add new documents to existing vector store."""
        if self.vector_store is None:
            self.vector_store = self.create_vector_store(documents)
        else:
            self.vector_store.add_documents(documents)
            # Note: Persistence is automatic in ChromaDB 0.4.x+
    
    def similarity_search(self, query: str, k: int = 5) -> List[Document]:
        """Perform similarity search in the vector store."""
        if self.vector_store is None:
            raise ValueError("Vector store not initialized. Please add documents first.")
        
        return self.vector_store.similarity_search(query, k=k)
    
    def similarity_search_with_score(self, query: str, k: int = 5) -> List[tuple]:
        """Perform similarity search with scores."""
        if self.vector_store is None:
            raise ValueError("Vector store not initialized. Please add documents first.")
        
        return self.vector_store.similarity_search_with_score(query, k=k)
    
    def delete_collection(self):
        """Delete the current collection."""
        if self.vector_store is not None:
            self.vector_store.delete_collection()
            self.vector_store = None
    
    def get_retriever(self, search_kwargs: dict = None):
        """Get a retriever for the vector store."""
        if self.vector_store is None:
            raise ValueError("Vector store not initialized. Please add documents first.")
        
        if search_kwargs is None:
            search_kwargs = {"k": 5}
        
        return self.vector_store.as_retriever(search_kwargs=search_kwargs) # Python