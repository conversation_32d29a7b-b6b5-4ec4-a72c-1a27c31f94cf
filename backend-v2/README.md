# 🧪 AI Test Case Generator

An intelligent test case generation system that uses **LangChain**, **Vector Database (ChromaDB)**, and **RAG (Retrieval-Augmented Generation)** to automatically generate comprehensive test cases from project requirements documentation.

## 🌟 Features

- **📄 Multiple Document Formats**: Supports PDF, DOCX, and TXT files
- **🤖 Agentic AI**: Uses LangChain agents for intelligent test case generation
- **🔍 RAG Architecture**: Retrieval-Augmented Generation for accurate, context-aware responses
- **💾 Vector Database**: ChromaDB for efficient document storage and similarity search
- **🎯 Comprehensive Testing**: Generates functional, non-functional, integration, and edge case tests
- **💬 API-Driven**: FastAPI backend for easy integration with any frontend
- **📊 Structured Output**: Well-formatted test cases with all necessary details

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Documents     │    │  Document       │    │  Vector Store   │
│   (PDF/DOCX/    │───▶│  Processor      │───▶│  (ChromaDB)     │
│    TXT)         │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                                                        ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   User Query    │───▶│  Test Case      │◀───│  RAG Retrieval  │
│                 │    │  Agent          │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │  Generated      │
                       │  Test Cases     │
                       └─────────────────┘
```

## 🚀 Quick Start

### 1. Installation

```bash
# Clone the repository
git clone <repository-url>
cd AI-Testcases/backend

# Install dependencies
pip install -r requirements.txt
```

### 2. Set Up Environment

```bash
# Set your Google API key
export GOOGLE_API_KEY=your_google_api_key_here

# Or create a .env file
echo "GOOGLE_API_KEY=your_google_api_key_here" > .env
```

### 3. Run the FastAPI Backend

```bash
uvicorn app:app --reload
```

The API will be available at [http://localhost:8000](http://localhost:8000).

---

### 🚢 Docker Deployment

You can build and run the FastAPI backend in a Docker container:

```bash
# Build the Docker image
docker build -t ai-testcase-generator-backend .

# Run the container (exposes FastAPI on port 8000)
docker run -p 8000:8000 --env-file .env ai-testcase-generator-backend
```

This will start the FastAPI app at [http://localhost:8000](http://localhost:8000).

## 🖥️ Frontend (React)

The web UI is now implemented in the `frontend/` folder using React and TypeScript. See the frontend README for setup and usage instructions.

## 🧩 Project Structure

```
AI-Testcases/
├── backend/
│   ├── app.py                  # FastAPI backend
│   ├── config.py               # Configuration settings
│   ├── document_processor.py   # Document processing utilities
│   ├── vector_store.py         # Vector database management
│   ├── test_case_agent.py      # Main agentic AI implementation
│   ├── requirements.txt        # Python dependencies
│   ├── README.md               # This file
│   ├── .env                    # Environment variables (not committed)
│   ├── .gitignore              # Git ignore rules
│   ├── .dockerignore           # Docker ignore rules
│   ├── Dockerfile              # Docker build instructions
│   ├── outputs/                # Generated outputs (ignored)
│   ├── uploads/                # Uploaded files (ignored)
│   └── vector_db/              # Vector database files (ignored)
├── frontend/                   # React frontend (see its README)
```

## 🔧 Configuration

Edit `config.py` to customize:

- **Embedding Model**: Default is `sentence-transformers/all-MiniLM-L6-v2`
- **LLM Model**: Default is `gemini-1.5-flash`
- **Chunk Size**: Default is 1000 characters
- **Temperature**: Default is 0.1 for more deterministic outputs

## 📊 Test Case Format

The system generates structured test cases with the following format:

```json
{
  "test_case_id": "TC_001",
  "title": "User Login with Valid Credentials",
  "objective": "Verify that user can login with valid credentials",
  "preconditions": [
    "User account exists",
    "Application is accessible"
  ],
  "test_steps": [
    "1. Navigate to login page",
    "2. Enter valid username",
    "3. Enter valid password",
    "4. Click login button"
  ],
  "expected_result": "User is successfully logged in and redirected to dashboard",
  "test_data": "username: testuser, password: Test123!",
  "priority": "High",
  "category": "Functional"
}
```

##  Test Categories

The AI generates test cases across multiple categories:

- **Functional Tests**: Core feature testing
- **Non-Functional Tests**: Performance, security, usability
- **Integration Tests**: API and system integration
- **Edge Cases**: Boundary and error conditions
- **Negative Tests**: Invalid input and error scenarios
- **User Experience Tests**: UI/UX validation

## 🛠️ API Reference

### FastAPI Endpoints

- `POST /upload`: Upload a requirements document and extract requirements text.
- `POST /generate`: Generate test cases from requirements text, test types, and detail level.

### Example Usage

```python
import requests

# Upload a document
with open("requirements.pdf", "rb") as f:
    res = requests.post("http://localhost:8000/upload", files={"file": f})
    requirements_text = res.json()["requirements_text"]

# Generate test cases
data = {
    "requirements_text": requirements_text,
    "test_types": ["Functional", "Integration"],
    "detail_level": "Detailed"
}
res = requests.post("http://localhost:8000/generate", data=data)
test_cases = res.json()["test_cases"]
```

## 🚨 Troubleshooting

### Common Issues

1. **Google API Key Error**
   ```bash
   export GOOGLE_API_KEY=your_key_here
   ```

2. **Module Import Errors**
   ```bash
   pip install -r requirements.txt
   ```

3. **Vector Store Issues**
   - Delete the `vector_db` folder and restart
   - Check file permissions

4. **Document Processing Errors**
   - Ensure files are not corrupted
   - Check file format (PDF, DOCX, TXT only)

## 🔮 Future Enhancements

- [ ] Support for more document formats (Markdown, HTML)
- [ ] Integration with test management tools (Jira, TestRail)
- [ ] Export to various formats (Excel, CSV, XML)
- [ ] Custom test case templates
- [ ] Integration with CI/CD pipelines
- [ ] Multi-language support
- [ ] Advanced analytics and reporting

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📞 Support

For support and questions:
- Create an issue in the repository
- Check the troubleshooting section

## 🙏 Acknowledgments

- **LangChain** for the agent framework
- **Google Gemini** for the language models
- **ChromaDB** for vector storage
- **Sentence Transformers** for embeddings

---

Built with using modern AI technologies for intelligent test case generation.
